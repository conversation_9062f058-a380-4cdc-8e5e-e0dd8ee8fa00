"""
群组成员管理处理器

处理群组成员扫描相关的命令
"""

import time
import asyncio
from typing import List
from datetime import datetime
from telegram import Update, ChatMember
from telegram.ext import ContextTypes
from telegram.error import TelegramError, Forbidden, BadRequest

from src.core.decorators import command_handler
from src.core.logger import default_logger as logger
from src.core.user_manager import user_manager
from src.models.user import GroupMember, ScanResult, UserType


def delete_message_by_id(context, chat_id: int, message_id: int):
    """删除指定消息的回调函数"""
    try:
        asyncio.create_task(context.bot.delete_message(chat_id=chat_id, message_id=message_id))
    except Exception as e:
        logger.warning(f"删除消息失败 (chat_id: {chat_id}, message_id: {message_id}): {e}")


@command_handler("scan_member", "扫描群组成员信息", priority=10)
async def handle_scan_member_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /scan_member 命令"""
    message = update.message
    if not message:
        return

    # 检查是否在群组中
    if message.chat.type not in ['group', 'supergroup']:
        group_only_message = await message.reply_text("❌ 此命令只能在群组中使用")
        
        # 1分钟后自动删除提示消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, group_only_message.message_id
            ),
            when=60
        )
        
        # 1分钟后自动删除用户命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=60
        )
        return

    # 检查机器人是否有管理员权限
    try:
        bot_member = await context.bot.get_chat_member(message.chat.id, context.bot.id)
        if bot_member.status not in ['administrator', 'creator']:
            permission_message = await message.reply_text(
                "❌ 机器人需要管理员权限才能扫描群组成员\n"
                "请将机器人设置为管理员后重试"
            )
            
            # 2分钟后自动删除提示消息
            context.job_queue.run_once(
                callback=lambda context: delete_message_by_id(
                    context, message.chat.id, permission_message.message_id
                ),
                when=120
            )
            
            # 2分钟后自动删除用户命令消息
            context.job_queue.run_once(
                callback=lambda context: delete_message_by_id(
                    context, message.chat.id, message.message_id
                ),
                when=120
            )
            return
            
    except Exception as e:
        logger.error(f"检查机器人权限时出错: {e}")
        error_message = await message.reply_text("❌ 检查权限时发生错误，请稍后重试")
        
        # 1分钟后自动删除错误消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, error_message.message_id
            ),
            when=60
        )
        return

    # 开始扫描
    group_id = message.chat.id
    group_title = message.chat.title or "未知群组"
    group_username = message.chat.username
    group_type = message.chat.type
    
    logger.info(f"开始扫描群组成员: {group_title} (ID: {group_id})")
    
    # 发送开始扫描的消息
    status_message = await message.reply_text(
        f"🔍 开始扫描群组成员...\n"
        f"📊 群组: {group_title}\n"
        f"⏳ 正在获取成员列表，请稍候..."
    )
    
    start_time = time.time()
    members_data = []
    total_members = 0
    scanned_members = 0
    regular_users = 0
    bots = 0
    error_message = None
    success = True
    
    try:
        # 获取群组成员数量
        try:
            chat_info = await context.bot.get_chat(group_id)
            total_members = getattr(chat_info, 'member_count', 0)
        except Exception as e:
            logger.warning(f"获取群组成员数量失败: {e}")
            total_members = 0
        
        # 更新状态消息
        await status_message.edit_text(
            f"🔍 正在扫描群组成员...\n"
            f"📊 群组: {group_title}\n"
            f"👥 预计成员数: {total_members}\n"
            f"⏳ 正在逐个获取成员信息..."
        )
        
        # 获取所有成员
        member_count = 0
        async for member in context.bot.iter_chat_members(group_id):
            try:
                member_count += 1
                
                # 每处理50个成员更新一次状态
                if member_count % 50 == 0:
                    await status_message.edit_text(
                        f"🔍 正在扫描群组成员...\n"
                        f"📊 群组: {group_title}\n"
                        f"👥 预计成员数: {total_members}\n"
                        f"✅ 已处理: {member_count} 个成员"
                    )
                
                user = member.user
                if not user:
                    continue

                # 获取成员加入时间
                joined_date = None
                try:
                    # 尝试获取成员的加入时间
                    if hasattr(member, 'joined_date') and member.joined_date:
                        joined_date = member.joined_date
                    elif hasattr(member, 'until_date') and member.until_date:
                        # 对于某些成员状态，可能有until_date
                        joined_date = member.until_date
                except Exception as e:
                    logger.debug(f"获取成员 {user.id} 加入时间失败: {e}")

                # 创建群组成员对象
                group_member = GroupMember.from_telegram_user(
                    user=user,
                    group_id=group_id,
                    group_title=group_title,
                    group_username=group_username,
                    group_type=group_type,
                    member_status=member.status,
                    joined_date=joined_date
                )
                
                members_data.append(group_member)
                scanned_members += 1
                
                if user.is_bot:
                    bots += 1
                else:
                    regular_users += 1
                    
            except Exception as e:
                logger.warning(f"处理成员 {member_count} 时出错: {e}")
                continue
        
        # 保存成员数据到数据库
        if members_data:
            await status_message.edit_text(
                f"💾 正在保存数据...\n"
                f"📊 群组: {group_title}\n"
                f"✅ 已扫描: {scanned_members} 个成员\n"
                f"👤 普通用户: {regular_users}\n"
                f"🤖 机器人: {bots}"
            )
            
            save_success = user_manager.save_members(members_data)
            if not save_success:
                error_message = "保存成员数据到数据库失败"
                success = False
        
    except Forbidden:
        error_message = "机器人没有权限获取群组成员列表"
        success = False
        logger.error(f"扫描群组成员失败: {error_message}")
        
    except BadRequest as e:
        error_message = f"请求错误: {str(e)}"
        success = False
        logger.error(f"扫描群组成员失败: {error_message}")
        
    except Exception as e:
        error_message = f"扫描过程中发生错误: {str(e)}"
        success = False
        logger.error(f"扫描群组成员失败: {error_message}")
    
    # 计算扫描耗时
    scan_duration = time.time() - start_time
    
    # 创建扫描结果
    scan_result = ScanResult(
        group_id=group_id,
        group_title=group_title,
        total_members=total_members,
        scanned_members=scanned_members,
        regular_users=regular_users,
        bots=bots,
        scan_time=datetime.now(),
        scan_duration=scan_duration,
        success=success,
        error_message=error_message
    )
    
    # 保存扫描记录
    user_manager.save_scan_record(scan_result)
    
    # 发送结果消息
    if success:
        result_text = (
            f"✅ **群组成员扫描完成**\n\n"
            f"📊 **群组信息：**\n"
            f"• 群组名称: {group_title}\n"
            f"• 群组ID: `{group_id}`\n"
            f"• 群组类型: {group_type}\n\n"
            f"👥 **扫描结果：**\n"
            f"• 总成员数: {total_members}\n"
            f"• 成功扫描: {scanned_members}\n"
            f"• 普通用户: {regular_users}\n"
            f"• 机器人: {bots}\n\n"
            f"⏱️ **扫描耗时:** {scan_duration:.2f} 秒\n"
            f"💾 **数据已保存到数据库**"
        )
    else:
        result_text = (
            f"❌ **群组成员扫描失败**\n\n"
            f"📊 **群组信息：**\n"
            f"• 群组名称: {group_title}\n"
            f"• 群组ID: `{group_id}`\n\n"
            f"❗ **错误信息：**\n"
            f"{error_message}\n\n"
            f"⏱️ **扫描耗时:** {scan_duration:.2f} 秒"
        )
    
    # 更新状态消息为最终结果
    final_message = await status_message.edit_text(result_text, parse_mode='Markdown')
    
    # 5分钟后自动删除结果消息和用户命令消息
    context.job_queue.run_once(
        callback=lambda context: delete_message_by_id(
            context, message.chat.id, final_message.message_id
        ),
        when=300  # 5分钟
    )
    
    context.job_queue.run_once(
        callback=lambda context: delete_message_by_id(
            context, message.chat.id, message.message_id
        ),
        when=300  # 5分钟
    )
    
    logger.info(f"群组成员扫描完成: {group_title} (ID: {group_id}), "
                f"成功: {success}, 扫描: {scanned_members}/{total_members}, "
                f"耗时: {scan_duration:.2f}秒")
